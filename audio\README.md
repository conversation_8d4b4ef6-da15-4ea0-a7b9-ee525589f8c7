# 音频文件说明

这个文件夹用于存放网站的音频文件。每个按钮对应一个音频文件。

## 需要的音频文件列表

### 名言区域音频文件：
- `q1.mp3` - "丢"
- `q2.mp3` - "啊丢"
- `q3.mp3` - "啊丢手绢"
- `q4.mp3` - "轻轻的放在小朋友的后边"
- `q5.mp3` - "大家不要告诉他"

### 语音区域音频文件：
- `v1.mp3` - "没座"
- `v2.mp3` - "没错"
- `v3.mp3` - "没做"
- `v4.mp3` - "A座"
- `v5.mp3` - "你懂音乐"
- `v6.mp3` - "因为我就是顶尖"
- `v7.mp3` - "我不这么认为"
- `v8.mp3` - "你是艺术家"
- `v9.mp3` - "没问题"
- `v10.mp3` - "你礼貌吗"
- `v11.mp3` - "阿坤是歌手"
- `v12.mp3` - "你很礼貌"
- `v13.mp3` - "无所谓"
- `v14.mp3` - "你礼貌吗"
- `v15.mp3` - "你要干什么"

### 说唱区域音频文件：
- `r1.mp3` - "他们朝我扔白菜"
- `r2.mp3` - "我拿白菜炒盘菜"
- `r3.mp3` - "他们朝我扔鸡蛋"
- `r4.mp3` - "我拿鸡蛋做蛋炒饭"
- `r5.mp3` - "他们朝我扔粑粑"
- `r6.mp3` - "我拿粑粑做蛋挞"
- `r7.mp3` - "他们朝我扔烟头"
- `r8.mp3` - "我捡起烟头抽两口"
- `r9.mp3` - "哦哦哦哦~"

## 注意事项

1. **音频文件格式**：建议使用 MP3 格式，确保浏览器兼容性
2. **文件命名**：必须严格按照上述列表命名，区分大小写
3. **音频质量**：建议使用适中的音质，文件大小控制在 100KB 以内
4. **备用方案**：如果音频文件不存在，网站会自动使用浏览器的语音合成功能（Text-to-Speech）

## 如何添加音频文件

1. 将对应的音频文件放入此 `audio` 文件夹
2. 确保文件名与上述列表完全匹配
3. 刷新网页即可使用真实音频文件

## 语音合成备用方案

如果没有提供音频文件，网站会自动使用浏览器内置的语音合成功能：
- 支持中文语音合成
- 自动朗读按钮文本
- 无需额外配置

这样即使没有音频文件，网站也能正常工作！
