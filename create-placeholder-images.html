<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建占位图片</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .placeholder { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>创建占位图片</h1>
    <p>如果原始图片无法显示，可以使用这个工具创建占位图片。</p>
    
    <div class="placeholder">
        <h3>占位图片预览</h3>
        <canvas id="canvas1" width="200" height="400"></canvas>
        <canvas id="canvas2" width="200" height="400"></canvas>
        <canvas id="canvas3" width="200" height="400"></canvas>
        <br>
        <button onclick="createPlaceholders()">生成占位图片</button>
        <button onclick="downloadImages()">下载图片</button>
    </div>
    
    <div class="placeholder">
        <h3>使用说明</h3>
        <ol>
            <li>点击"生成占位图片"按钮</li>
            <li>点击"下载图片"按钮保存图片</li>
            <li>将下载的图片重命名为 p1.png, p2.png, p3.png</li>
            <li>放入 audio 文件夹中</li>
            <li>刷新主页面查看效果</li>
        </ol>
    </div>

    <script>
        function createPlaceholders() {
            const colors = [
                ['#FF6B6B', '#4ECDC4'], // 红色到青色
                ['#45B7D1', '#96CEB4'], // 蓝色到绿色
                ['#FECA57', '#FF9FF3']  // 黄色到粉色
            ];
            
            const texts = ['图片 1', '图片 2', '图片 3'];
            
            for (let i = 0; i < 3; i++) {
                const canvas = document.getElementById(`canvas${i + 1}`);
                const ctx = canvas.getContext('2d');
                
                // 创建渐变背景
                const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
                gradient.addColorStop(0, colors[i][0]);
                gradient.addColorStop(1, colors[i][1]);
                
                // 填充背景
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 添加装饰图案
                ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
                for (let j = 0; j < 20; j++) {
                    const x = Math.random() * canvas.width;
                    const y = Math.random() * canvas.height;
                    const radius = Math.random() * 30 + 10;
                    ctx.beginPath();
                    ctx.arc(x, y, radius, 0, Math.PI * 2);
                    ctx.fill();
                }
                
                // 添加文字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(texts[i], canvas.width / 2, canvas.height / 2);
                
                // 添加边框
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
                ctx.lineWidth = 2;
                ctx.strokeRect(0, 0, canvas.width, canvas.height);
            }
        }
        
        function downloadImages() {
            for (let i = 0; i < 3; i++) {
                const canvas = document.getElementById(`canvas${i + 1}`);
                const link = document.createElement('a');
                link.download = `p${i + 1}.png`;
                link.href = canvas.toDataURL();
                link.click();
            }
        }
        
        // 页面加载时自动生成图片
        window.onload = function() {
            createPlaceholders();
        };
    </script>
</body>
</html>
