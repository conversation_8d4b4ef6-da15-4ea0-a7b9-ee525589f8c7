// 音乐播放器控制脚本
class MusicPlayer {
    constructor() {
        this.isPlaying = false;
        this.isLooping = false;
        this.currentProgress = 0;
        this.progressInterval = null;
        this.playDuration = 30000; // 30秒播放时长
        
        this.initializeElements();
        this.bindEvents();
        this.updateStatus();
    }

    initializeElements() {
        this.playBtn = document.getElementById('playBtn');
        this.stopBtn = document.getElementById('stopBtn');
        this.loopBtn = document.getElementById('loopBtn');
        this.statusText = document.getElementById('statusText');
        this.progressFill = document.getElementById('progressFill');
        
        // 获取内容区域用于播放动画
        this.sections = document.querySelectorAll('.section');
    }

    bindEvents() {
        this.playBtn.addEventListener('click', () => this.togglePlay());
        this.stopBtn.addEventListener('click', () => this.stop());
        this.loopBtn.addEventListener('click', () => this.toggleLoop());
        
        // 为语音和说唱区域添加点击播放功能
        this.addClickToPlay();
    }

    addClickToPlay() {
        // 语音区域点击播放
        const voiceItems = document.querySelectorAll('.voice-list p');
        voiceItems.forEach((item, index) => {
            item.addEventListener('click', () => {
                this.playVoiceItem(item, index);
            });
        });

        // 说唱区域点击播放
        const rapItems = document.querySelectorAll('.rap-lyrics p');
        rapItems.forEach((item, index) => {
            item.addEventListener('click', () => {
                this.playRapItem(item, index);
            });
        });

        // 名言区域点击播放
        const quoteText = document.querySelector('.quote-text');
        quoteText.addEventListener('click', () => {
            this.playQuote();
        });
    }

    togglePlay() {
        if (this.isPlaying) {
            this.pause();
        } else {
            this.play();
        }
    }

    play() {
        this.isPlaying = true;
        this.updatePlayButton();
        this.updateStatus('正在播放...');
        this.startProgress();
        this.addPlayingAnimation();
        
        // 模拟音频播放结束
        setTimeout(() => {
            if (this.isPlaying) {
                this.onPlaybackEnd();
            }
        }, this.playDuration);
    }

    pause() {
        this.isPlaying = false;
        this.updatePlayButton();
        this.updateStatus('已暂停');
        this.stopProgress();
        this.removePlayingAnimation();
    }

    stop() {
        this.isPlaying = false;
        this.currentProgress = 0;
        this.updatePlayButton();
        this.updateStatus('已停止');
        this.stopProgress();
        this.updateProgressBar();
        this.removePlayingAnimation();
    }

    toggleLoop() {
        this.isLooping = !this.isLooping;
        this.updateLoopButton();
        this.updateStatus(this.isLooping ? '单曲循环已开启' : '单曲循环已关闭');
    }

    onPlaybackEnd() {
        if (this.isLooping) {
            this.currentProgress = 0;
            this.play();
        } else {
            this.stop();
        }
    }

    startProgress() {
        this.stopProgress();
        this.progressInterval = setInterval(() => {
            this.currentProgress += 100;
            this.updateProgressBar();
            
            if (this.currentProgress >= this.playDuration) {
                this.onPlaybackEnd();
            }
        }, 100);
    }

    stopProgress() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
    }

    updateProgressBar() {
        const percentage = (this.currentProgress / this.playDuration) * 100;
        this.progressFill.style.width = `${Math.min(percentage, 100)}%`;
    }

    updatePlayButton() {
        const icon = this.playBtn.querySelector('.btn-icon');
        const text = this.playBtn.querySelector('.btn-text');
        
        if (this.isPlaying) {
            icon.textContent = '⏸️';
            text.textContent = '暂停';
            this.playBtn.classList.add('active');
        } else {
            icon.textContent = '▶️';
            text.textContent = '播放';
            this.playBtn.classList.remove('active');
        }
    }

    updateLoopButton() {
        if (this.isLooping) {
            this.loopBtn.classList.add('active');
            this.loopBtn.style.background = 'linear-gradient(135deg, #38b2ac, #319795)';
        } else {
            this.loopBtn.classList.remove('active');
            this.loopBtn.style.background = 'linear-gradient(135deg, #4299e1, #3182ce)';
        }
    }

    updateStatus(message) {
        this.statusText.textContent = `状态：${message}`;
    }

    addPlayingAnimation() {
        this.sections.forEach(section => {
            section.classList.add('playing');
        });
    }

    removePlayingAnimation() {
        this.sections.forEach(section => {
            section.classList.remove('playing');
        });
    }

    // 播放特定语音项目
    playVoiceItem(item, index) {
        this.highlightItem(item);
        this.updateStatus(`正在播放语音 ${index + 1}`);
        this.simulateItemPlay(item);
    }

    // 播放特定说唱项目
    playRapItem(item, index) {
        this.highlightItem(item);
        this.updateStatus(`正在播放说唱 ${index + 1}`);
        this.simulateItemPlay(item);
    }

    // 播放名言
    playQuote() {
        const quoteText = document.querySelector('.quote-text');
        this.highlightItem(quoteText);
        this.updateStatus('正在播放名言');
        this.simulateItemPlay(quoteText);
    }

    highlightItem(item) {
        // 移除其他高亮
        document.querySelectorAll('.highlighted').forEach(el => {
            el.classList.remove('highlighted');
        });
        
        // 添加高亮样式
        item.classList.add('highlighted');
        item.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
        item.style.color = 'white';
        item.style.transform = 'scale(1.02)';
    }

    simulateItemPlay(item) {
        setTimeout(() => {
            item.classList.remove('highlighted');
            item.style.background = '';
            item.style.color = '';
            item.style.transform = '';
            this.updateStatus('播放完成');
        }, 3000);
    }
}

// 页面加载完成后初始化播放器
document.addEventListener('DOMContentLoaded', () => {
    const player = new MusicPlayer();
    
    // 添加一些额外的交互效果
    addInteractiveEffects();
});

function addInteractiveEffects() {
    // 为按钮添加点击波纹效果
    const buttons = document.querySelectorAll('.control-btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // 添加键盘快捷键支持
    document.addEventListener('keydown', (e) => {
        switch(e.code) {
            case 'Space':
                e.preventDefault();
                document.getElementById('playBtn').click();
                break;
            case 'KeyS':
                document.getElementById('stopBtn').click();
                break;
            case 'KeyL':
                document.getElementById('loopBtn').click();
                break;
        }
    });
}

// 添加波纹效果的CSS
const style = document.createElement('style');
style.textContent = `
    .control-btn {
        position: relative;
        overflow: hidden;
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .highlighted {
        transition: all 0.3s ease !important;
    }
`;
document.head.appendChild(style);
