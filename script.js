// 音频播放器控制脚本
class AudioPlayer {
    constructor() {
        this.currentAudio = null;
        this.isLooping = false;
        this.audioQueue = [];
        this.isPlaying = false;
        this.currentProgress = 0;
        this.progressInterval = null;

        this.initializeElements();
        this.bindEvents();
        this.updateStatus();
        this.createAudioElements();
    }

    initializeElements() {
        this.playBtn = document.getElementById('playBtn');
        this.stopBtn = document.getElementById('stopBtn');
        this.loopBtn = document.getElementById('loopBtn');
        this.statusText = document.getElementById('statusText');
        this.progressFill = document.getElementById('progressFill');

        // 获取所有音频按钮
        this.audioButtons = document.querySelectorAll('.audio-btn');
    }

    createAudioElements() {
        // 创建音频元素映射
        this.audioElements = {};

        this.audioButtons.forEach(button => {
            const audioId = button.dataset.audio;
            const audio = new Audio();

            // 设置音频文件路径（如果文件不存在，会使用合成音频）
            audio.src = `audio/${audioId}.mp3`;

            // 音频加载失败时的处理
            audio.addEventListener('error', () => {
                console.log(`音频文件 ${audioId}.mp3 不存在，将使用合成音频`);
                this.createSyntheticAudio(audioId, button.textContent);
            });

            // 音频播放结束事件
            audio.addEventListener('ended', () => {
                this.onAudioEnded(button);
            });

            this.audioElements[audioId] = audio;
        });
    }

    bindEvents() {
        this.playBtn.addEventListener('click', () => this.toggleGlobalPlay());
        this.stopBtn.addEventListener('click', () => this.stopAll());
        this.loopBtn.addEventListener('click', () => this.toggleLoop());

        // 为每个音频按钮绑定点击事件
        this.audioButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.playAudio(button);
            });
        });
    }

    // 播放指定音频
    playAudio(button) {
        const audioId = button.dataset.audio;
        const audio = this.audioElements[audioId];

        // 停止当前播放的音频
        this.stopCurrentAudio();

        // 设置当前音频
        this.currentAudio = audio;
        this.currentButton = button;

        // 播放音频
        if (audio) {
            audio.currentTime = 0;
            audio.play().then(() => {
                this.onAudioStart(button);
            }).catch(error => {
                console.log('音频播放失败，使用合成音频:', error);
                this.playSyntheticAudio(button.textContent, button);
            });
        } else {
            this.playSyntheticAudio(button.textContent, button);
        }
    }

    // 创建合成音频（使用Web Speech API）
    createSyntheticAudio(audioId, text) {
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'zh-CN';
            utterance.rate = 1;
            utterance.pitch = 1;
            this.audioElements[audioId] = utterance;
        }
    }

    // 播放合成音频
    playSyntheticAudio(text, button) {
        if ('speechSynthesis' in window) {
            // 停止之前的语音合成
            speechSynthesis.cancel();

            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'zh-CN';
            utterance.rate = 1;
            utterance.pitch = 1;

            utterance.onstart = () => {
                this.onAudioStart(button);
            };

            utterance.onend = () => {
                this.onAudioEnded(button);
            };

            speechSynthesis.speak(utterance);
            this.currentAudio = utterance;
        } else {
            // 如果不支持语音合成，使用视觉反馈
            this.simulateAudioPlayback(button);
        }
    }

    // 模拟音频播放（视觉反馈）
    simulateAudioPlayback(button) {
        this.onAudioStart(button);
        setTimeout(() => {
            this.onAudioEnded(button);
        }, 2000);
    }

    // 音频开始播放
    onAudioStart(button) {
        this.isPlaying = true;
        button.classList.add('playing');
        this.updateStatus(`正在播放: ${button.textContent}`);
        this.updatePlayButton();
        this.startProgress();
    }

    // 音频播放结束
    onAudioEnded(button) {
        button.classList.remove('playing');

        if (this.isLooping && this.currentButton === button) {
            // 如果开启循环，重新播放
            setTimeout(() => {
                this.playAudio(button);
            }, 500);
        } else {
            this.isPlaying = false;
            this.currentAudio = null;
            this.currentButton = null;
            this.updateStatus('播放完成');
            this.updatePlayButton();
            this.stopProgress();
            this.currentProgress = 0;
            this.updateProgressBar();
        }
    }

    // 停止当前播放的音频
    stopCurrentAudio() {
        if (this.currentAudio) {
            if (this.currentAudio instanceof Audio) {
                this.currentAudio.pause();
                this.currentAudio.currentTime = 0;
            } else {
                // 停止语音合成
                speechSynthesis.cancel();
            }
        }

        // 移除所有播放状态
        this.audioButtons.forEach(btn => {
            btn.classList.remove('playing');
        });

        this.stopProgress();
    }

    // 全局播放控制
    toggleGlobalPlay() {
        if (this.isPlaying) {
            this.pauseAll();
        } else {
            this.resumeOrPlayFirst();
        }
    }

    pauseAll() {
        this.stopCurrentAudio();
        this.isPlaying = false;
        this.updatePlayButton();
        this.updateStatus('已暂停');
    }

    resumeOrPlayFirst() {
        if (this.currentButton) {
            this.playAudio(this.currentButton);
        } else {
            // 播放第一个按钮
            const firstButton = this.audioButtons[0];
            if (firstButton) {
                this.playAudio(firstButton);
            }
        }
    }

    stopAll() {
        this.stopCurrentAudio();
        this.isPlaying = false;
        this.currentAudio = null;
        this.currentButton = null;
        this.currentProgress = 0;
        this.updatePlayButton();
        this.updateStatus('已停止');
        this.updateProgressBar();
    }

    toggleLoop() {
        this.isLooping = !this.isLooping;
        this.updateLoopButton();
        this.updateStatus(this.isLooping ? '单曲循环已开启' : '单曲循环已关闭');
    }

    startProgress() {
        this.stopProgress();
        this.currentProgress = 0;
        const duration = 3000; // 3秒进度条

        this.progressInterval = setInterval(() => {
            this.currentProgress += 50;
            this.updateProgressBar();

            if (this.currentProgress >= duration) {
                this.stopProgress();
            }
        }, 50);
    }

    stopProgress() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
    }

    updateProgressBar() {
        const duration = 3000;
        const percentage = (this.currentProgress / duration) * 100;
        this.progressFill.style.width = `${Math.min(percentage, 100)}%`;
    }

    updatePlayButton() {
        const icon = this.playBtn.querySelector('.btn-icon');
        const text = this.playBtn.querySelector('.btn-text');

        if (this.isPlaying) {
            icon.textContent = '⏸️';
            text.textContent = '暂停';
            this.playBtn.classList.add('active');
        } else {
            icon.textContent = '▶️';
            text.textContent = '播放';
            this.playBtn.classList.remove('active');
        }
    }

    updateLoopButton() {
        if (this.isLooping) {
            this.loopBtn.classList.add('active');
            this.loopBtn.style.background = 'linear-gradient(135deg, #38b2ac, #319795)';
        } else {
            this.loopBtn.classList.remove('active');
            this.loopBtn.style.background = 'linear-gradient(135deg, #4299e1, #3182ce)';
        }
    }

    updateStatus(message) {
        this.statusText.textContent = `状态：${message}`;
    }
}

// 页面加载完成后初始化播放器
document.addEventListener('DOMContentLoaded', () => {
    const player = new AudioPlayer();

    // 添加一些额外的交互效果
    addInteractiveEffects();

    // 初始化图片功能
    initializeImageGallery();

    // 初始化状态
    player.updateStatus('就绪 - 点击任意按钮播放音频');

    // 显示调试信息
    showDebugInfo();
});

function addInteractiveEffects() {
    // 为所有按钮添加点击波纹效果
    const allButtons = document.querySelectorAll('.control-btn, .audio-btn');
    allButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // 添加键盘快捷键支持
    document.addEventListener('keydown', (e) => {
        // 避免在输入框中触发快捷键
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }

        switch(e.code) {
            case 'Space':
                e.preventDefault();
                document.getElementById('playBtn').click();
                break;
            case 'KeyS':
                e.preventDefault();
                document.getElementById('stopBtn').click();
                break;
            case 'KeyL':
                e.preventDefault();
                document.getElementById('loopBtn').click();
                break;
            case 'Digit1':
            case 'Digit2':
            case 'Digit3':
            case 'Digit4':
            case 'Digit5':
            case 'Digit6':
            case 'Digit7':
            case 'Digit8':
            case 'Digit9':
                e.preventDefault();
                const index = parseInt(e.code.replace('Digit', '')) - 1;
                const buttons = document.querySelectorAll('.audio-btn');
                if (buttons[index]) {
                    buttons[index].click();
                }
                break;
        }
    });

    // 添加鼠标悬停音效提示
    const audioButtons = document.querySelectorAll('.audio-btn');
    audioButtons.forEach((button, index) => {
        button.title = `点击播放: ${button.textContent} (快捷键: ${index + 1})`;
    });
}

// 添加波纹效果和其他样式的CSS
const style = document.createElement('style');
style.textContent = `
    .control-btn, .audio-btn {
        position: relative;
        overflow: hidden;
    }

    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .audio-btn.playing {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
        transform: translateY(-2px) scale(1.05);
    }

    .audio-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }

    /* 响应式按钮布局 */
    @media (max-width: 768px) {
        .audio-buttons {
            justify-content: center;
        }

        .audio-btn {
            min-width: 80px;
            font-size: 0.8rem;
            padding: 6px 12px;
        }
    }
`;
document.head.appendChild(style);

// 图片画廊功能
function initializeImageGallery() {
    const images = document.querySelectorAll('.gallery-image');
    const overlay = createImageOverlay();

    images.forEach((image, index) => {
        // 确保图片可见
        image.style.opacity = '1';
        image.style.display = 'block';

        // 添加点击事件
        image.addEventListener('click', () => {
            enlargeImage(image, overlay);
        });

        // 添加错误处理
        image.addEventListener('error', () => {
            console.log(`图片 ${image.src} 加载失败`);
            console.log(`图片路径: ${image.src}`);
            image.style.display = 'none';
            image.alt = '图片加载失败';
        });

        // 添加加载成功处理
        image.addEventListener('load', () => {
            console.log(`图片 ${index + 1} 加载成功: ${image.src}`);
            image.style.opacity = '1';
        });

        // 检查图片是否已经加载完成
        if (image.complete) {
            if (image.naturalWidth > 0) {
                console.log(`图片 ${index + 1} 已缓存: ${image.src}`);
                image.style.opacity = '1';
            } else {
                console.log(`图片 ${index + 1} 加载失败（缓存）: ${image.src}`);
                image.style.display = 'none';
            }
        }

        // 设置过渡效果
        image.style.transition = 'all 0.3s ease';
    });
}

// 创建图片遮罩层
function createImageOverlay() {
    const overlay = document.createElement('div');
    overlay.className = 'image-overlay';
    document.body.appendChild(overlay);

    // 点击遮罩层关闭放大图片
    overlay.addEventListener('click', () => {
        closeEnlargedImage(overlay);
    });

    return overlay;
}

// 放大图片
function enlargeImage(image, overlay) {
    // 创建放大的图片副本
    const enlargedImage = image.cloneNode(true);
    enlargedImage.className = 'gallery-image enlarged';
    enlargedImage.style.opacity = '1';

    // 显示遮罩层和放大图片
    overlay.classList.add('active');
    document.body.appendChild(enlargedImage);

    // 添加关闭按钮
    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '✕';
    closeBtn.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1001;
        background: rgba(255, 255, 255, 0.9);
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        font-size: 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    `;

    closeBtn.addEventListener('mouseenter', () => {
        closeBtn.style.background = 'rgba(255, 255, 255, 1)';
        closeBtn.style.transform = 'scale(1.1)';
    });

    closeBtn.addEventListener('mouseleave', () => {
        closeBtn.style.background = 'rgba(255, 255, 255, 0.9)';
        closeBtn.style.transform = 'scale(1)';
    });

    closeBtn.addEventListener('click', () => {
        closeEnlargedImage(overlay);
    });

    document.body.appendChild(closeBtn);

    // ESC键关闭
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            closeEnlargedImage(overlay);
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);

    // 存储关闭按钮引用
    overlay.closeBtn = closeBtn;
    overlay.enlargedImage = enlargedImage;
    overlay.handleEscape = handleEscape;
}

// 关闭放大图片
function closeEnlargedImage(overlay) {
    overlay.classList.remove('active');

    // 移除放大的图片和关闭按钮
    if (overlay.enlargedImage) {
        overlay.enlargedImage.remove();
    }
    if (overlay.closeBtn) {
        overlay.closeBtn.remove();
    }
    if (overlay.handleEscape) {
        document.removeEventListener('keydown', overlay.handleEscape);
    }
}

// 调试信息显示
function showDebugInfo() {
    const debugInfo = document.getElementById('debugInfo');
    const debugContent = document.getElementById('debugContent');

    if (!debugInfo || !debugContent) return;

    // 检查是否在开发环境（localhost）
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        debugInfo.style.display = 'block';

        let info = '<p><strong>图片加载状态:</strong></p>';
        const images = document.querySelectorAll('.gallery-image');

        images.forEach((img, index) => {
            const status = img.complete && img.naturalWidth > 0 ? '✅ 已加载' : '❌ 未加载';
            info += `<p>图片 ${index + 1}: ${status} - ${img.src}</p>`;
        });

        info += `<p><strong>总共找到 ${images.length} 张图片</strong></p>`;
        info += `<p><strong>当前URL:</strong> ${window.location.href}</p>`;

        debugContent.innerHTML = info;

        // 5秒后自动隐藏调试信息
        setTimeout(() => {
            debugInfo.style.display = 'none';
        }, 10000);
    }
}
