/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    color: white;
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 10px;
}

/* 主要内容网格 */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 区域样式 */
.section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.section:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.section h2 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.3rem;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 8px;
}

.content-box {
    background: #f7fafc;
    border-radius: 10px;
    padding: 15px;
    border-left: 4px solid #667eea;
}

/* 名言区域特殊样式 */
.quote-section .content-box {
    border-left-color: #48bb78;
}

.quote-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #2d3748;
    font-style: italic;
    text-align: center;
}

/* 语音区域样式 */
.voice-section .content-box {
    border-left-color: #ed8936;
}

.voice-list p {
    margin-bottom: 8px;
    padding: 8px 12px;
    background: white;
    border-radius: 8px;
    border-left: 3px solid #ed8936;
    transition: background-color 0.2s ease;
}

.voice-list p:hover {
    background-color: #fff5f0;
    cursor: pointer;
}

/* 说唱区域样式 */
.rap-section .content-box {
    border-left-color: #9f7aea;
}

.rap-lyrics p {
    margin-bottom: 10px;
    padding: 10px;
    background: white;
    border-radius: 8px;
    line-height: 1.6;
    transition: background-color 0.2s ease;
}

.rap-lyrics p:hover {
    background-color: #faf5ff;
    cursor: pointer;
}

.rap-outro {
    text-align: center;
    font-weight: bold;
    color: #9f7aea;
    font-size: 1.2rem;
}

/* 功能区域样式 */
.control-section .content-box {
    border-left-color: #38b2ac;
}

.controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.control-btn {
    flex: 1;
    min-width: 120px;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.play-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.stop-btn {
    background: linear-gradient(135deg, #f56565, #e53e3e);
    color: white;
}

.loop-btn {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.control-btn:active {
    transform: translateY(0);
}

.control-btn.active {
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
}

/* 状态显示 */
.status {
    text-align: center;
}

#statusText {
    margin-bottom: 10px;
    font-weight: bold;
    color: #4a5568;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

/* 底部样式 */
footer {
    text-align: center;
    color: white;
    margin-top: 30px;
    padding: 20px;
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .controls {
        flex-direction: column;
    }
    
    .control-btn {
        min-width: auto;
    }
}

/* 动画效果 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.playing {
    animation: pulse 2s infinite;
}

.btn-icon {
    font-size: 1.2rem;
}

.btn-text {
    font-size: 0.9rem;
}
