/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 顶部图片区域样式 */
.top-images {
    margin-bottom: 30px;
}

.image-gallery {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    gap: 15px;
    flex-wrap: wrap;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    margin-bottom: 20px;
}

.gallery-image {
    max-width: 200px;
    max-height: 400px;
    width: auto;
    height: auto;
    object-fit: cover;
    border-radius: 10px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    cursor: pointer;
    opacity: 1; /* 默认显示，JavaScript会处理加载动画 */
    display: block;
    background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
    min-height: 200px; /* 确保即使图片加载失败也有占位空间 */
}

/* 图片加载失败时的样式 */
.gallery-image[alt*="失败"]::before {
    content: "图片加载中...";
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: #6c757d;
    font-size: 14px;
    border-radius: 10px;
}

.gallery-image:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 12px 40px rgba(0,0,0,0.3);
}

/* 图片点击放大效果 */
.gallery-image.enlarged {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(1.5);
    z-index: 1000;
    max-width: 80vw;
    max-height: 80vh;
    border-radius: 15px;
}

.image-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 999;
    display: none;
}

.image-overlay.active {
    display: block;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    color: white;
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 10px;
}

/* 主要内容网格 */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 区域样式 */
.section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.section:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.section h2 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.3rem;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 8px;
}

.content-box {
    background: #f7fafc;
    border-radius: 10px;
    padding: 15px;
    border-left: 4px solid #667eea;
}

/* 音频按钮容器 */
.audio-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: flex-start;
}

/* 音频按钮样式 */
.audio-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f7fafc, #edf2f7);
    color: #4a5568;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.audio-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.audio-btn:active {
    transform: translateY(0);
}

.audio-btn.playing {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    animation: pulse 1.5s infinite;
}

/* 名言区域按钮样式 */
.quote-section .content-box {
    border-left-color: #48bb78;
}

.quote-section .audio-btn {
    background: linear-gradient(135deg, #f0fff4, #c6f6d5);
    border-color: #48bb78;
}

.quote-section .audio-btn:hover {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

/* 语音区域按钮样式 */
.voice-section .content-box {
    border-left-color: #ed8936;
}

.voice-section .audio-btn {
    background: linear-gradient(135deg, #fffaf0, #fbd38d);
    border-color: #ed8936;
}

.voice-section .audio-btn:hover {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    color: white;
}

/* 说唱区域按钮样式 */
.rap-section .content-box {
    border-left-color: #9f7aea;
}

.rap-section .audio-btn {
    background: linear-gradient(135deg, #faf5ff, #e9d8fd);
    border-color: #9f7aea;
}

.rap-section .audio-btn:hover {
    background: linear-gradient(135deg, #9f7aea, #805ad5);
    color: white;
}

.rap-outro {
    font-weight: bold;
    font-size: 1.1rem;
    padding: 12px 20px !important;
}

/* 功能区域样式 */
.control-section .content-box {
    border-left-color: #38b2ac;
}

.controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.control-btn {
    flex: 1;
    min-width: 120px;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.play-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.stop-btn {
    background: linear-gradient(135deg, #f56565, #e53e3e);
    color: white;
}

.loop-btn {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.control-btn:active {
    transform: translateY(0);
}

.control-btn.active {
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
}

/* 状态显示 */
.status {
    text-align: center;
}

#statusText {
    margin-bottom: 10px;
    font-weight: bold;
    color: #4a5568;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

/* 底部样式 */
footer {
    text-align: center;
    color: white;
    margin-top: 30px;
    padding: 20px;
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header h1 {
        font-size: 2rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .controls {
        flex-direction: column;
    }

    .control-btn {
        min-width: auto;
    }

    /* 移动端图片样式 */
    .image-gallery {
        gap: 10px;
        padding: 15px;
    }

    .gallery-image {
        max-width: 120px;
        max-height: 250px;
    }

    .gallery-image.enlarged {
        max-width: 95vw;
        max-height: 95vh;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* 动画效果 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.playing {
    animation: pulse 2s infinite;
}

.btn-icon {
    font-size: 1.2rem;
}

.btn-text {
    font-size: 0.9rem;
}
