<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .image-test {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .test-image {
            max-width: 200px;
            max-height: 300px;
            border: 1px solid #ccc;
            margin: 10px;
        }
        .status {
            margin-top: 10px;
            padding: 5px;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .loading { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>图片加载测试</h1>
        <p>这个页面用于测试图片文件是否能正常加载。</p>
        
        <div class="image-test">
            <h3>图片1 (p1.png)</h3>
            <img src="audio/p1.png" alt="图片1" class="test-image" id="img1">
            <div class="status loading" id="status1">加载中...</div>
        </div>
        
        <div class="image-test">
            <h3>图片2 (p2.png)</h3>
            <img src="audio/p2.png" alt="图片2" class="test-image" id="img2">
            <div class="status loading" id="status2">加载中...</div>
        </div>
        
        <div class="image-test">
            <h3>图片3 (p3.png)</h3>
            <img src="audio/p3.png" alt="图片3" class="test-image" id="img3">
            <div class="status loading" id="status3">加载中...</div>
        </div>
        
        <div class="image-test">
            <h3>文件列表检查</h3>
            <button onclick="checkFiles()">检查文件是否存在</button>
            <div id="fileCheck"></div>
        </div>
    </div>

    <script>
        // 测试每个图片的加载状态
        function testImage(imgId, statusId) {
            const img = document.getElementById(imgId);
            const status = document.getElementById(statusId);
            
            img.onload = function() {
                status.textContent = `✅ 加载成功 - 尺寸: ${this.naturalWidth}×${this.naturalHeight}`;
                status.className = 'status success';
            };
            
            img.onerror = function() {
                status.textContent = '❌ 加载失败 - 文件不存在或格式错误';
                status.className = 'status error';
            };
            
            // 如果图片已经加载完成
            if (img.complete) {
                if (img.naturalWidth > 0) {
                    img.onload();
                } else {
                    img.onerror();
                }
            }
        }
        
        // 检查文件是否存在
        async function checkFiles() {
            const fileCheck = document.getElementById('fileCheck');
            const files = ['audio/p1.png', 'audio/p2.png', 'audio/p3.png'];
            let results = '<h4>文件检查结果:</h4>';
            
            for (const file of files) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (response.ok) {
                        results += `<p style="color: green;">✅ ${file} - 存在</p>`;
                    } else {
                        results += `<p style="color: red;">❌ ${file} - 不存在 (${response.status})</p>`;
                    }
                } catch (error) {
                    results += `<p style="color: red;">❌ ${file} - 网络错误</p>`;
                }
            }
            
            fileCheck.innerHTML = results;
        }
        
        // 页面加载完成后测试所有图片
        window.onload = function() {
            testImage('img1', 'status1');
            testImage('img2', 'status2');
            testImage('img3', 'status3');
        };
    </script>
</body>
</html>
