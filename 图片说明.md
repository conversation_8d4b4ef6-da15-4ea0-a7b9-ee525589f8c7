# 图片文件说明

## 需要的图片文件

网站顶部需要3张图片，文件名和规格如下：

### 图片文件列表：
- `p1.png` - 第一张图片
- `p2.png` - 第二张图片
- `p3.png` - 第三张图片

### 图片规格要求：
- **尺寸**：1179 × 2556 像素
- **格式**：JPG 格式（也支持PNG、WebP等）
- **方向**：竖向长图
- **文件大小**：建议控制在 2MB 以内，确保加载速度

## 图片功能特性

### 🖼️ **显示效果**
- 图片在页面顶部水平排列
- 自动适配不同屏幕尺寸
- 具有圆角和阴影效果
- 鼠标悬停时有轻微放大和上浮效果

### 🔍 **交互功能**
- **点击放大**：点击任意图片可以全屏查看
- **关闭方式**：
  - 点击图片外的黑色区域
  - 点击右上角的 ✕ 按钮
  - 按 ESC 键
- **响应式设计**：在手机上自动调整大小

### 📱 **移动端适配**
- 手机端图片会自动缩小显示
- 点击放大后适配手机屏幕
- 触摸友好的交互设计

## 如何添加图片

### 方法1：直接放置文件
1. 将图片文件重命名为 `p1.png`、`p2.png`、`p3.png`
2. 将文件放在 `audio` 文件夹中
3. 刷新网页即可看到图片

### 方法2：使用其他格式
如果您的图片是其他格式（如PNG），需要修改HTML文件：
1. 打开 `index.html` 文件
2. 找到图片标签，将 `.jpg` 改为对应格式
3. 例如：`src="p1.jpg"` 改为 `src="p1.png"`

## 图片加载说明

### ✅ **正常加载**
- 图片会有淡入效果
- 加载完成后显示完整图片
- 支持点击放大查看

### ❌ **加载失败**
- 如果图片文件不存在，该图片会自动隐藏
- 不会影响其他图片的显示
- 控制台会显示加载失败的提示

### 🔄 **替换图片**
- 直接替换同名文件即可
- 建议清除浏览器缓存以看到最新图片
- 可以使用 Ctrl+F5 强制刷新

## 技术细节

### 支持的图片格式：
- JPG/JPEG
- PNG  
- WebP
- GIF（静态）

### 浏览器兼容性：
- Chrome、Firefox、Safari、Edge 等现代浏览器
- 支持移动端浏览器
- 自动降级处理不支持的功能

## 注意事项

1. **文件命名**：必须严格按照 `p1.png`、`p2.png`、`p3.png` 命名
2. **文件位置**：放在 `audio` 文件夹中
3. **图片质量**：建议使用高质量图片，系统会自动优化显示
4. **加载速度**：文件过大可能影响网页加载速度
5. **版权问题**：请确保使用的图片有合法使用权

## 故障排除

### 图片不显示？
1. 检查文件名是否正确（区分大小写）
2. 检查文件是否在正确位置
3. 检查图片文件是否损坏
4. 尝试清除浏览器缓存

### 图片显示模糊？
1. 检查原图片分辨率是否足够
2. 确保图片质量设置较高
3. 避免过度压缩图片

### 点击放大不工作？
1. 检查浏览器是否支持JavaScript
2. 确保没有其他脚本冲突
3. 尝试刷新页面重新加载

现在您可以将图片文件放入网站目录，享受完整的图片浏览体验！
